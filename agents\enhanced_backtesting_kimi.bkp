#!/usr/bin/env python3
"""
Enhanced Backtesting System with Polars and AsyncIO - PERFORMANCE OPTIMIZED
- Fixed major performance bottlenecks in signal processing
- Optimized memory usage and reduced excessive result generation
- Added proper batching and vectorized operations
- Improved trade simulation logic to reduce false signals
"""

import os
import re 
import logging
import yaml
import polars as pl
import asyncio
from pathlib import Path
import gc
import time
from typing import List, Dict, Any, Optional, Tuple, Union
import math
import random
import numpy as np

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s %(levelname)s:%(message)s')
logger = logging.getLogger(__name__)

# Configuration paths
STRATEGIES_FILE = Path(__file__).with_suffix('').parent.parent / "config" / "strategies.yaml"

# Configuration
DATA_DIR = "data/features"
OUTPUT_DIR = "data/backtest"
OUTPUT_FORMAT = "parquet"
COMPRESSION = "brotli"
RISK_REWARD_RATIOS = [[1, 1.5], [1, 2], [1.5, 2], [2, 3]]
RISK_PER_TRADE_PCT = 1.0
INITIAL_CAPITAL = 100000
TRANSACTION_COST_PCT = 0.05
SLIPPAGE_PCT = 0.02
PROFIT_THRESHOLD = 1.0
INTRADAY_MARGIN_MULTIPLIER = 3.5

# Performance optimization settings
MIN_SIGNAL_DISTANCE = 5  # Minimum bars between signals
MIN_VOLUME_THRESHOLD = 1000  # Minimum volume for valid signals
MAX_HOLDING_PERIOD = 50  # Maximum bars to hold position
MIN_PRICE_MOVE = 0.001  # Minimum price movement to consider valid signal

# Execution-mode switches
USE_PROCESS_POOL_EXECUTOR = False  # Disabled for debugging
CONCURRENT_FILES = 4
TIMEFRAMES = ["1min", "3min", "5min", "15min"]

# GPU detection
try:
    import cupy as cp
    GPU_AVAILABLE = True
except ImportError:
    GPU_AVAILABLE = False

def generate_strategy_signals(df: pl.DataFrame, side: str, strategy: dict) -> pl.Series:
    """Optimized signal generation with better filtering"""
    expr_str = strategy.get(side, "").strip()
    if not expr_str:
        return pl.Series("mask", [False] * df.height)

    # Pre-process the string: Python → Polars
    expr_str = re.sub(r"\.rolling\((\d+)\)\.mean\(\)", r".rolling_mean(\1)", expr_str)
    expr_str = re.sub(r"\.rolling\((\d+)\)\.max\(\)", r".rolling_max(\1)", expr_str)
    expr_str = re.sub(r"\.rolling\((\d+)\)\.min\(\)", r".rolling_min(\1)", expr_str)
    
    expr_str = (expr_str
                .replace(" and ", " & ")
                .replace(" or ",  " | ")
                .replace(" not ", " ~"))

    # Safe evaluation context with available columns only
    available_cols = set(df.columns)
    ctx = {}
    
    # Only add columns that exist in the dataframe
    for col in ['close', 'open', 'high', 'low', 'volume', 'datetime']:
        if col in available_cols:
            ctx[col] = pl.col(col)
    
    # Add technical indicators if they exist
    for col in df.columns:
        if any(indicator in col.lower() for indicator in ['rsi', 'ema', 'sma', 'macd', 'bb', 'atr', 'vwap', 'cpr', 'supertrend']):
            ctx[col] = pl.col(col)
    
    ctx.update({
        "abs":   lambda x: x.abs(),
        "min":   pl.min_horizontal,
        "max":   pl.max_horizontal,
        "sqrt":  lambda x: x.sqrt(),
        "log":   lambda x: x.log(),
    })

    try:
        # Evaluate the expression
        mask = eval(expr_str, {"__builtins__": {}}, ctx)
        if isinstance(mask, pl.Expr):
            result = df.select(mask).to_series()
            
            # Apply additional filters to reduce noise
            if result.sum() > 0:  # Only if we have signals
                # Filter by volume threshold if volume column exists
                if 'volume' in available_cols:
                    volume_filter = df.select(pl.col('volume') >= MIN_VOLUME_THRESHOLD).to_series()
                    result = result & volume_filter
                
                # Filter by minimum price movement
                if 'close' in available_cols:
                    price_change = df.select((pl.col('close') / pl.col('close').shift(1) - 1).abs() >= MIN_PRICE_MOVE).to_series()
                    result = result & price_change
                
            return result
        else:
            return pl.Series("mask", [bool(mask)] * df.height)

    except Exception as e:
        logger.debug(f"Expression failed ({side}): {expr_str} – {e}")
        return pl.Series("mask", [False] * df.height)

def filter_signals_by_distance(signals: pl.Series, min_distance: int = MIN_SIGNAL_DISTANCE) -> pl.Series:
    """Filter signals to maintain minimum distance between them"""
    if signals.sum() == 0:
        return signals
    
    signal_indices = signals.to_numpy().nonzero()[0]
    if len(signal_indices) <= 1:
        return signals
    
    # Filter signals that are too close together
    filtered_indices = [signal_indices[0]]  # Always keep first signal
    
    for idx in signal_indices[1:]:
        if idx - filtered_indices[-1] >= min_distance:
            filtered_indices.append(idx)
    
    # Create new signal series
    filtered_signals = [False] * len(signals)
    for idx in filtered_indices:
        filtered_signals[idx] = True
    
    return pl.Series(filtered_signals)

async def simulate_trades_vectorized(df: pl.DataFrame, strategy: Dict[str, Any], rr: List[float], timeframe: str) -> Optional[List[Dict[str, Any]]]:
    """Optimized trade simulation with better filtering"""
    try:
        # Basic data validation and sorting
        df = df.sort("datetime").drop_nulls(subset=['close', 'high', 'low', 'open', 'volume', 'datetime'])
        if len(df) < 50:  # Increased minimum data requirement
            return None

        # Generate signals
        long_signals = generate_strategy_signals(df, "long", strategy)
        short_signals = generate_strategy_signals(df, "short", strategy)
        
        # Apply distance filtering to reduce excessive signals
        long_signals = filter_signals_by_distance(long_signals)
        short_signals = filter_signals_by_distance(short_signals)
        
        # Early exit if no signals
        if long_signals.sum() == 0 and short_signals.sum() == 0:
            return None
        
        # Combine signals and add metadata
        df_signals = df.with_columns([
            pl.when(long_signals).then(1).when(short_signals).then(-1).otherwise(0).alias("signal"),
            pl.int_range(pl.len()).alias("row_idx"),
        ])
        
        # Filter to only signal rows and limit the number of signals
        signals_only = df_signals.filter(pl.col("signal").is_in([1, -1]))
        
        # Limit maximum number of signals to process
        max_signals = min(100, len(signals_only))  # Process max 100 signals per symbol/strategy
        if len(signals_only) > max_signals:
            signals_only = signals_only.head(max_signals)
            logger.info(f"Limited signals to {max_signals} for performance")
        
        if len(signals_only) == 0:
            return None

        return await process_signals_vectorized(df_signals, signals_only, strategy, rr, timeframe)
        
    except Exception as e:
        logger.error(f"Vectorized trade simulation failed: {e}")
        return None

async def process_signals_vectorized(df_all: pl.DataFrame, signals_df: pl.DataFrame, strategy: Dict[str, Any], rr: List[float], timeframe: str) -> List[Dict[str, Any]]:
    """Optimized signal processing with batch operations"""
    trades = []
    capital = strategy.get('capital', INITIAL_CAPITAL)
    
    # Convert to numpy for faster iteration
    signal_data = signals_df.select(['row_idx', 'signal', 'close', 'datetime']).to_numpy()
    
    for i, (entry_idx, signal_type, entry_price, entry_time) in enumerate(signal_data):
        # Calculate position size
        stop_loss_price = entry_price * (1 - rr[0] / 100) if signal_type == 1 else entry_price * (1 + rr[0] / 100)
        position_value, quantity = calculate_intraday_position_size(capital, entry_price, stop_loss_price, signal_type)
        
        if quantity <= 0:
            continue
        
        # Find exit with optimized logic
        profit_target = entry_price * (1 + rr[1] / 100) if signal_type == 1 else entry_price * (1 - rr[1] / 100)
        exit_data = find_exit_vectorized_polars_optimized(df_all, int(entry_idx), signal_type, profit_target, stop_loss_price, timeframe)
        
        if exit_data is None:
            continue
        
        exit_price, holding_period, exit_reason = exit_data
        
        # Skip very short-term trades (likely noise)
        if holding_period < 2:
            continue
        
        # Calculate PnL
        trade_pnl, trade_pnl_pct = calculate_trade_pnl_fast(signal_type, entry_price, exit_price, quantity, position_value)
        
        # Only keep trades with meaningful PnL (filter out tiny moves)
        if abs(trade_pnl_pct) < 0.1:  # Less than 0.1% move
            continue
        
        trades.append({
            'entry_time': entry_time,
            'entry_price': entry_price,
            'exit_price': exit_price,
            'signal': signal_type,
            'pnl': trade_pnl,
            'pnl_pct': trade_pnl_pct,
            'position_size': position_value,
            'quantity': quantity,
            'holding_period': holding_period,
            'exit_reason': exit_reason
        })
        
        # Update capital for next trade (simple approach)
        capital += trade_pnl
        
        # Stop if we've processed enough trades for this strategy
        if len(trades) >= 50:  # Limit trades per strategy to manage results
            logger.info(f"Limited to {len(trades)} trades for performance")
            break
    
    return trades

def find_exit_vectorized_polars_optimized(df: pl.DataFrame, entry_idx: int, signal_type: int, profit_target: float, stop_loss: float, timeframe: str) -> Optional[Tuple[float, int, str]]:
    """Optimized exit finding with smaller look-ahead window"""
    try:
        # Reduced look-ahead window for faster processing
        max_look_ahead = min(MAX_HOLDING_PERIOD, len(df) - entry_idx - 1)
        if max_look_ahead <= 0:
            return None
        
        future_data = df.slice(entry_idx + 1, max_look_ahead)
        if len(future_data) == 0:
            return None

        # Vectorized exit condition checking
        if signal_type == 1:  # Long position
            exit_conditions = future_data.with_columns([
                (pl.col("high") >= profit_target).alias("profit_hit"),
                (pl.col("low") <= stop_loss).alias("stop_hit"),
                pl.int_range(pl.len()).alias("period")
            ])
        else:  # Short position
            exit_conditions = future_data.with_columns([
                (pl.col("low") <= profit_target).alias("profit_hit"),
                (pl.col("high") >= stop_loss).alias("stop_hit"),
                pl.int_range(pl.len()).alias("period")
            ])

        # Find first exit condition
        first_exit = exit_conditions.filter(
            pl.col("profit_hit") | pl.col("stop_hit")
        ).head(1)
        
        if len(first_exit) > 0:
            exit_row = first_exit.row(0, named=True)
            holding_period = exit_row['period'] + 1
            
            if exit_row['profit_hit']:
                return profit_target, holding_period, "profit"
            else:
                return stop_loss, holding_period, "stop"
        
        # No exit condition met, use end-of-day exit
        if len(future_data) > 0:
            last_row = future_data.tail(1).row(0, named=True)
            return last_row['close'], len(future_data), "eod"
        
        return None
        
    except Exception as e:
        logger.debug(f"Optimized exit finding failed: {e}")
        return None

def calculate_intraday_position_size(capital: float, entry_price: float, stop_loss_price: float, signal_type: int) -> Tuple[float, int]:
    """Calculate position size based on risk management"""
    risk_per_share = abs(entry_price - stop_loss_price)
    if risk_per_share <= 0:
        return 0, 0
    
    risk_amount = capital * (RISK_PER_TRADE_PCT / 100)
    quantity = int(risk_amount / risk_per_share)
    
    if quantity <= 0:
        return 0, 0
    
    position_value = quantity * entry_price
    max_position_value = capital * INTRADAY_MARGIN_MULTIPLIER
    
    if position_value > max_position_value:
        quantity = int(max_position_value / entry_price)
        position_value = quantity * entry_price
    
    return position_value, quantity

def calculate_trade_pnl_fast(signal_type: int, entry_price: float, exit_price: float, quantity: float, position_value: float) -> Tuple[float, float]:
    """Fast PnL calculation"""
    if signal_type == 1:  # Long
        trade_pnl = (exit_price - entry_price) * quantity
    else:  # Short
        trade_pnl = (entry_price - exit_price) * quantity
    
    trade_pnl_pct = (trade_pnl / position_value) * 100 if position_value > 0 else 0
    
    # Apply transaction costs
    transaction_cost = position_value * (TRANSACTION_COST_PCT / 100)
    trade_pnl -= transaction_cost
    trade_pnl_pct -= TRANSACTION_COST_PCT
    
    return trade_pnl, trade_pnl_pct

def calculate_performance_metrics(trades: List[Dict[str, Any]], symbol: str, strategy_name: str, timeframe: str, rr_combo: List[float]) -> Optional[Dict[str, Any]]:
    """Calculate performance metrics with better validation"""
    if not trades or len(trades) < 3:  # Require minimum trades for meaningful metrics
        return None
    
    total_pnl = sum(t['pnl'] for t in trades)
    total_pnl_pct = sum(t['pnl_pct'] for t in trades)
    winning_trades = sum(1 for t in trades if t['pnl'] > 0)
    total_trades = len(trades)
    
    accuracy = winning_trades / total_trades if total_trades > 0 else 0
    expectancy = total_pnl / total_trades if total_trades > 0 else 0
    
    avg_win = sum(t['pnl'] for t in trades if t['pnl'] > 0) / winning_trades if winning_trades > 0 else 0
    losing_trades = total_trades - winning_trades
    avg_loss = sum(t['pnl'] for t in trades if t['pnl'] < 0) / losing_trades if losing_trades > 0 else 0
    
    profit_factor = avg_win / abs(avg_loss) if avg_loss != 0 else float('inf')
    
    # Calculate drawdown
    cumulative_pnl = []
    running_pnl = 0
    for trade in trades:
        running_pnl += trade['pnl_pct']
        cumulative_pnl.append(running_pnl)
    
    max_drawdown = 0
    peak = cumulative_pnl[0]
    for pnl in cumulative_pnl:
        if pnl > peak:
            peak = pnl
        drawdown = peak - pnl
        if drawdown > max_drawdown:
            max_drawdown = drawdown
    
    # Calculate Sharpe ratio
    pnl_series = [t['pnl_pct'] for t in trades]
    if len(pnl_series) > 1:
        std_dev = np.std(pnl_series)
        sharpe_ratio = (np.mean(pnl_series) * np.sqrt(252)) / std_dev if std_dev > 0 else 0
    else:
        sharpe_ratio = 0
    
    return {
        'stock_name': symbol,
        'strategy_name': strategy_name,
        'timeframe': timeframe,
        'risk_reward_ratio': f"{rr_combo[0]}:{rr_combo[1]}",
        'total_trades': total_trades,
        'winning_trades': winning_trades,
        'accuracy': round(accuracy * 100, 2),
        'total_pnl': round(total_pnl, 2),
        'roi': round(total_pnl_pct, 2),
        'expectancy': round(expectancy, 2),
        'avg_win': round(avg_win, 2),
        'avg_loss': round(avg_loss, 2),
        'profit_factor': round(profit_factor, 2),
        'max_drawdown': round(max_drawdown, 2),
        'sharpe_ratio': round(sharpe_ratio, 2),
        'avg_holding_period': round(sum(t['holding_period'] for t in trades) / total_trades, 1),
        'is_profitable': total_pnl_pct > PROFIT_THRESHOLD
    }

# File loading and processing functions
def load_strategies() -> List[Dict[str, Any]]:
    """Load strategies from YAML file"""
    try:
        with open(STRATEGIES_FILE, 'r', encoding='utf-8') as f:
            data = yaml.safe_load(f)
        strategies = data.get('strategies', [])
        logger.info(f"[LOAD] Loaded {len(strategies)} strategies")
        return strategies
    except Exception as e:
        logger.error(f"Failed to load strategies from {STRATEGIES_FILE}: {e}")
        return []

def get_available_feature_files() -> List[Tuple[str, str, str]]:
    """Get available feature files"""
    feature_files = []
    for file_path in Path(DATA_DIR).glob("*.parquet"):
        filename = file_path.name
        symbol, timeframe = extract_symbol_and_timeframe_from_filename(filename)
        if symbol and timeframe and timeframe in TIMEFRAMES:
            feature_files.append((str(file_path), symbol, timeframe))
    logger.info(f"[FOUND] {len(feature_files)} feature files")
    return feature_files

def extract_symbol_and_timeframe_from_filename(filename: str) -> Tuple[Optional[str], Optional[str]]:
    """Extract symbol and timeframe from filename"""
    try:
        stem = Path(filename).stem
        if stem.startswith("features_"):
            stem = stem[9:]
        parts = stem.split('_')
        timeframe = parts[-1] if parts[-1] in TIMEFRAMES else None
        symbol = '_'.join(parts[:-1]) if timeframe else None
        return symbol, timeframe
    except Exception as e:
        logger.debug(f"Failed to extract symbol/timeframe from {filename}: {e}")
    return None, None

def generate_output_filename(symbol: str, timeframe: str) -> str:
    """Generate output filename"""
    return f"backtest_{symbol}_{timeframe}.parquet"

async def write_symbol_results_async(results: List[Dict[str, Any]], symbol: str, timeframe: str):
    """Write results to file"""
    if not results:
        return
    
    try:
        output_filename = generate_output_filename(symbol, timeframe)
        output_path = os.path.join(OUTPUT_DIR, output_filename)
        
        # Ensure output directory exists
        Path(OUTPUT_DIR).mkdir(parents=True, exist_ok=True)
        
        # Write results
        pl.DataFrame(results).write_parquet(output_path, compression=COMPRESSION)
        logger.info(f"[SUCCESS] Written {len(results)} results for {symbol} to {output_filename}")
    except Exception as e:
        logger.error(f"[ERROR] Error writing results for {symbol}: {e}")

# Main processing functions
async def process_files_sequential_optimized(files, strategies, total_files):
    """Sequential processing with optimizations"""
    processed_files = 0
    
    for idx, (file_path, symbol, timeframe) in enumerate(files, 1):
        start_time = time.time()
        logger.info(f"[{idx}/{total_files}] Processing {symbol} ({timeframe})...")
        
        try:
            # Load data
            df = pl.read_parquet(file_path)
            logger.info(f"Loaded {len(df)} rows for {symbol}")
            
            all_results = []
            
            # Process strategies
            for strategy_idx, strategy in enumerate(strategies):
                strategy_start = time.time()
                logger.info(f"  Strategy {strategy_idx + 1}/{len(strategies)}: {strategy['name']}")
                
                # Process risk-reward combinations
                for rr in RISK_REWARD_RATIOS:
                    trades = await simulate_trades_vectorized(df, strategy, rr, timeframe)
                    
                    if trades and len(trades) >= 3:  # Only process if we have meaningful trades
                        metrics = calculate_performance_metrics(trades, symbol, strategy['name'], timeframe, rr)
                        if metrics:
                            all_results.append(metrics)
                
                strategy_time = time.time() - strategy_start
                logger.info(f"    Completed in {strategy_time:.2f}s")
            
            # Write results if any
            if all_results:
                await write_symbol_results_async(all_results, symbol, timeframe)
            
            file_time = time.time() - start_time
            logger.info(f"  File processed in {file_time:.2f}s, generated {len(all_results)} results")
            
            processed_files += 1
            
            # Memory cleanup every 10 files
            if processed_files % 10 == 0:
                gc.collect()
                logger.info("Memory cleanup performed")
                
        except Exception as e:
            logger.error(f"Error processing {symbol}: {e}")
            continue

async def process_files_parallel_optimized(files, strategies, total_files):
    """Parallel processing (fallback to sequential for now)"""
    await process_files_sequential_optimized(files, strategies, total_files)

# Utility functions
def aggressive_memory_cleanup():
    """Aggressive memory cleanup"""
    gc.collect()

def reset_polars_state():
    """Reset Polars state"""
    pass

def init_executors():
    """Initialize executors"""
    pass

def cleanup_executors():
    """Cleanup executors"""
    pass

# Main async function
async def main_async():
    """Main asynchronous entry point"""
    logger.info("[INIT] Starting Enhanced Backtesting System - PERFORMANCE OPTIMIZED")
    logger.info("=" * 80)
    
    start_time = time.time()
    
    # Load strategies
    strategies = load_strategies()
    if not strategies:
        logger.error("[ERROR] No strategies loaded, exiting")
        return
    
    # Get files
    feature_files = get_available_feature_files()
    if not feature_files:
        logger.error("[ERROR] No feature files found, exiting")
        return
    
    logger.info(f"[INFO] Processing {len(feature_files)} files with {len(strategies)} strategies")
    
    # Process files
    total_files = len(feature_files)
    await process_files_sequential_optimized(feature_files, strategies, total_files)
    
    # Summary
    end_time = time.time()
    total_time = end_time - start_time
    
    logger.info("🎉 BACKTESTING COMPLETED!")
    logger.info(f"⏱️ Total time: {total_time:.1f} seconds")
    logger.info(f"📊 Files processed: {total_files}")
    
    # Output summary
    output_files = list(Path(OUTPUT_DIR).glob("backtest_*.parquet"))
    if output_files:
        total_size = sum(f.stat().st_size for f in output_files) / (1024 * 1024)
        logger.info(f"[OUTPUT] Generated {len(output_files)} files ({total_size:.1f} MB)")

# Main function for direct execution
async def main():
    """Main function for direct execution"""
    await main_async()

if __name__ == "__main__":
    asyncio.run(main())