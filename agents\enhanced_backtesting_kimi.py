
        #!/usr/bin/env python3
"""
Enhanced Backtesting System with Polars and AsyncIO - PERFORMANCE OPTIMIZED
- Fixed major performance bottlenecks in signal processing
- Optimized memory usage and reduced excessive result generation
- Added proper batching and vectorized operations
- Improved trade simulation logic to reduce false signals
- FIXED: generate_strategy_signals function name error
"""

import os
import re 
import logging
import yaml
import polars as pl
import asyncio
from pathlib import Path
import gc
import time
from typing import List, Dict, Any, Optional, Tuple, Union
import math
import random
import sys
import concurrent.futures # Added for ProcessPoolExecutor
import numpy # Added for vectorbt
import vectorbt as vbt # Added for GPU vectorization
import numexpr # Added for vectorbt performance
os.environ["OMP_NUM_THREADS"] = "1" 

if sys.platform == "win32":
    os.environ["PYTHONIOENCODING"] = "utf-8"

# Setup logging with INFO level for general runs, DEBUG can be enabled via run_enhanced_backtesting_kimi.py --log-level DEBUG
logging.basicConfig(level=logging.INFO, format='%(asctime)s %(levelname)s:%(message)s')
logger = logging.getLogger(__name__)

# GPU detection
try:
    import cupy as cp
    np = cp # Alias cupy to np if GPU is available
    GPU_AVAILABLE = True
    logger.info("CuPy (GPU) is available and will be used.")
except ImportError:
    np = numpy # Fallback to numpy if CuPy is not available
    GPU_AVAILABLE = False
    logger.info("CuPy (GPU) is NOT available. Falling back to NumPy (CPU).")

# Main function for direct execution
async def main():
    """Main function for direct execution"""
    await main_async()

def debug_quick_test():
    """Debug function to test with minimal data and simple strategies"""
    logger.info("=== QUICK DEBUG TEST ===")
    
    # Load strategies for debugging
    strategies = load_strategies()
    if not strategies:
        logger.error("No strategies loaded - check your strategies.yaml file")
        return
    
    logger.info(f"Loaded {len(strategies)} strategies:")
    for i, strategy in enumerate(strategies):
        logger.info(f"  {i+1}. {strategy.get('name', 'Unnamed')}")
        logger.info(f"     Long: {strategy.get('long', 'NOT_DEFINED')}")
        logger.info(f"     Short: {strategy.get('short', 'NOT_DEFINED')}")
    
    # Check for feature files
    feature_files = get_available_feature_files()
    if not feature_files:
        logger.error("No feature files found - check your data/features directory")
        return
    
    logger.info(f"Found {len(feature_files)} feature files:")
    for i, (path, symbol, timeframe) in enumerate(feature_files[:5]):  # Show first 5
        logger.info(f"  {i+1}. {symbol} ({timeframe})")
    
    # Test with first file
    if feature_files:
        test_file, test_symbol, test_timeframe = feature_files[0]
        logger.info(f"Testing with: {test_symbol} ({test_timeframe})")

# Configuration paths
STRATEGIES_FILE = Path(__file__).with_suffix('').parent.parent / "config" / "strategies.yaml"

# Configuration
DATA_DIR = "data/features"
OUTPUT_DIR = "data/backtest"
TEMP_DIR = "data/backtest/temp" # New temporary directory
OUTPUT_FORMAT = "parquet"
COMPRESSION = "brotli"
RISK_REWARD_RATIOS = [[1, 2], [1.5, 2], [2, 3]]
RISK_PER_TRADE_PCT = 1.0
INITIAL_CAPITAL = 100000
TRANSACTION_COST_PCT = 0.05
SLIPPAGE_PCT = 0.02
PROFIT_THRESHOLD = 1.0
INTRADAY_MARGIN_MULTIPLIER = 3.5

# Performance optimization settings (RELAXED for better signal capture)
MIN_SIGNAL_DISTANCE = 3  # Minimum bars between signals (reduced from 5)
MIN_VOLUME_THRESHOLD = 100  # Minimum volume for valid signals (reduced from 1000)
MAX_HOLDING_PERIOD = 100  # Maximum bars to hold position (increased from 50)
MIN_PRICE_MOVE = 0.0005  # Minimum price movement (reduced from 0.001)
# MAX_TRADES_PER_STRATEGY = 1500 # Default maximum trades per strategy - Removed as per user request

# Execution-mode switches
USE_PROCESS_POOL_EXECUTOR = True  # Changed from False to True
CONCURRENT_FILES = 12
TIMEFRAMES = ["1min", "3min", "5min", "15min"]

# Global variable for the process pool executor
process_executor: Optional[concurrent.futures.ProcessPoolExecutor] = None

def init_executors():
    global process_executor
    if USE_PROCESS_POOL_EXECUTOR:
        logger.info(f"Initializing ProcessPoolExecutor with {CONCURRENT_FILES} workers")
        process_executor = concurrent.futures.ProcessPoolExecutor(max_workers=CONCURRENT_FILES)

def cleanup_executors():
    global process_executor
    if process_executor:
        logger.info("Shutting down ProcessPoolExecutor")
        process_executor.shutdown(wait=True)
        process_executor = None

def generate_strategy_signals(df: pl.DataFrame, side: str, strategy: dict) -> pl.Series:
    """Generate strategy signals with enhanced debugging and validation"""
    expr_str = strategy.get(side, "").strip()
    strategy_name = strategy.get('name', 'Unknown')
    
    if not expr_str:
        logger.debug(f"No {side} expression for strategy {strategy_name}")
        return pl.Series("mask", [False] * df.height)

    logger.debug(f"Processing {side} signal for {strategy_name}: {expr_str}")
    
    # Check available columns in dataframe
    available_cols = set(df.columns)
    logger.debug(f"Available columns: {sorted(available_cols)}")

    # Manual replacement approach - more reliable
    replacements = {
        'close': 'pl.col("close")',
        'open': 'pl.col("open")',
        'high': 'pl.col("high")',
        'low': 'pl.col("low")',
        'volume': 'pl.col("volume")',
        'rsi_14': 'pl.col("rsi_14")',
        'rsi_5': 'pl.col("rsi_5")',
        'ema_5': 'pl.col("ema_5")',
        'ema_10': 'pl.col("ema_10")',
        'ema_13': 'pl.col("ema_13")',
        'ema_20': 'pl.col("ema_20")',
        'ema_21': 'pl.col("ema_21")',
        'ema_50': 'pl.col("ema_50")',
        'vwap': 'pl.col("vwap")',
        'supertrend': 'pl.col("supertrend")',
        'cpr_top': 'pl.col("cpr_top")',
        'cpr_bottom': 'pl.col("cpr_bottom")',
        'macd': 'pl.col("macd")',
        'macd_signal': 'pl.col("macd_signal")',
        'bb_upper': 'pl.col("bb_upper")',
        'bb_lower': 'pl.col("bb_lower")',
        'bb_middle': 'pl.col("bb_middle")',
        'atr': 'pl.col("atr")',
        'adx': 'pl.col("adx")',
        'cci': 'pl.col("cci")',
        'mfi': 'pl.col("mfi")',
        'stoch_k': 'pl.col("stoch_k")',
        'stoch_d': 'pl.col("stoch_d")',
        'pivot': 'pl.col("pivot")',
        'resistance': 'pl.col("resistance")',
        'support': 'pl.col("support")',
        'donchian_high': 'pl.col("donchian_high")',
        'donchian_low': 'pl.col("donchian_low")',
    }
    
    # Replace boolean operators first
    expr_str = (expr_str
                .replace(" and ", " & ")
                .replace(" or ",  " | ")
                .replace(" not ", " ~"))
    
    # Check if required columns exist before replacement
    missing_cols = []
    for col_name in replacements.keys():
        if col_name in expr_str and col_name not in available_cols:
            missing_cols.append(col_name)
    
    if missing_cols:
        logger.warning(f"Missing columns for {strategy_name} {side}: {missing_cols}")
        return pl.Series("mask", [False] * df.height)
    
    # Replace column names (only if they exist in the dataframe)
    for col_name, pl_expr in replacements.items():
        if col_name in available_cols:
            # Use word boundaries to avoid partial matches
            pattern = r'\b' + re.escape(col_name) + r'\b'
            expr_str = re.sub(pattern, pl_expr, expr_str)

    # Add parentheses around each logical block to ensure correct precedence
    # This is crucial for expressions like A > B & C > D where Python's & has higher precedence than >
    expr_str = "( " + expr_str.replace(" & ", " ) & ( ").replace(" | ", " ) | ( ") + " )"

    logger.debug(f"Converted expression: {expr_str}")
    logger.debug(f"Final expression for eval: {expr_str}") # Added for debugging

    try:
        # Evaluate the expression
        mask = eval(expr_str, {"__builtins__": {}, "pl": pl})
        if isinstance(mask, pl.Expr):
            result = df.select(mask.alias("signal")).to_series()
            signal_count = result.sum()
            logger.debug(f"Generated {signal_count} {side} signals for {strategy_name}")
            return result
        else:
            logger.debug(f"Expression returned constant value: {mask}")
            return pl.Series("mask", [bool(mask)] * df.height)

    except Exception as e:
        logger.warning(f"Expression failed ({side}) for {strategy_name}: {expr_str} – {e}")
        return pl.Series("mask", [False] * df.height)

def filter_signals_by_distance(signals: pl.Series, min_distance: int = MIN_SIGNAL_DISTANCE) -> pl.Series:
    """Filter signals to maintain minimum distance between them - RELAXED"""
    if signals.sum() == 0:
        return signals
    
    signal_indices = signals.arg_true().to_list() # Get indices of true signals using Polars
    if len(signal_indices) <= 1:
        return signals
    
    # More relaxed filtering - allow closer signals if they're strong
    filtered_indices = [signal_indices[0]]  # Always keep first signal
    
    for idx in signal_indices[1:]:
        if idx - filtered_indices[-1] >= min_distance:
            filtered_indices.append(idx)
        elif len(filtered_indices) < 10:  # Allow some close signals if we don't have many
            filtered_indices.append(idx)
    
    # Create new signal series
    filtered_signals_array = [False] * len(signals)
    for idx in filtered_indices:
        filtered_signals_array[idx] = True
    
    return pl.Series(signals.name, filtered_signals_array)

def process_signals_vectorized(df: pl.DataFrame, long_signals: pl.Series, short_signals: pl.Series, strategy: Dict[str, Any], rr: List[float], timeframe: str) -> Optional[List[Dict[str, Any]]]:
    """Process signals using vectorbt for backtesting."""
    try:
        strategy_name = strategy.get('name', 'Unknown')
        logger.debug(f"Processing signals with vectorbt for {strategy_name}")

        # Convert Polars Series to NumPy/CuPy arrays
        # Use .to_numpy() which will return CuPy array if np is aliased to cp
        # Convert Polars Series to NumPy/CuPy arrays and reshape to 2D for vectorbt
        price_data = df["close"].to_numpy(allow_copy=True).astype(np.float64).reshape(-1, 1)
        open_data = df["open"].to_numpy(allow_copy=True).astype(np.float64).reshape(-1, 1)
        high_data = df["high"].to_numpy(allow_copy=True).astype(np.float64).reshape(-1, 1)
        low_data = df["low"].to_numpy(allow_copy=True).astype(np.float64).reshape(-1, 1)
        
        entries = long_signals.to_numpy(allow_copy=True).astype(np.bool_).reshape(-1, 1)
        exits = short_signals.to_numpy(allow_copy=True).astype(np.bool_).reshape(-1, 1)

        # Create a portfolio
        # Pass the reshaped 2D arrays to from_signals
        pf = vbt.Portfolio.from_signals(
            price_data,
            entries,
            exits,
            init_cash=INITIAL_CAPITAL,
            fees=TRANSACTION_COST_PCT / 100.0,
            slippage=SLIPPAGE_PCT / 100.0,
        )

        # Access the trades for the first (and only) column
        # pf.trades.values returns a 1D array of TradeSeries objects if there's one column.
        # We need to iterate over the individual trades within that single TradeSeries.
        trade_series = pf.trades.values[0]

        if trade_series.count() == 0:
            logger.warning(f"No trades generated by vectorbt for {strategy_name}")
            return None

        # Extract trades and convert to desired format
        trades_list = []
        # Iterate over individual trade objects within the single TradeSeries
        for trade in trade_series:
            # Determine signal_type based on trade direction
            signal_type = trade.direction # 1 for long, -1 for short
            
            holding_period = trade.exit_idx - trade.entry_idx
            
            quantity = float(trade.size)
            entry_price = float(trade.entry_price)
            exit_price = float(trade.exit_price)
            
            position_value = quantity * entry_price
            trade_pnl = float(trade.pnl)
            trade_pnl_pct = (trade_pnl / position_value) * 100 if position_value > 0 else 0

            trades_list.append({
                'entry_datetime': df["datetime"][trade.entry_idx],
                'exit_datetime': df["datetime"][trade.exit_idx],
                'entry_price': entry_price,
                'exit_price': exit_price,
                'signal_type': signal_type,
                'pnl': round(trade_pnl, 2),
                'pnl_pct': round(trade_pnl_pct, 2),
                'holding_period': int(holding_period),
                'quantity': quantity,
                'position_value': position_value,
                'stop_loss_price': None, 
                'take_profit_price': None, 
            })
        
        logger.debug(f"Generated {len(trades_list)} trades using vectorbt for {strategy_name}")
        return trades_list

    except Exception as e:
        logger.error(f"vectorbt processing failed for {strategy.get('name', 'Unknown')}: {e}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        return None

def simulate_trades_vectorized(df: pl.DataFrame, strategy: Dict[str, Any], rr: List[float], timeframe: str) -> Optional[List[Dict[str, Any]]]:
    """Optimized trade simulation with enhanced debugging"""
    try:
        strategy_name = strategy.get('name', 'Unknown')
        logger.debug(f"Starting trade simulation for {strategy_name}")
        
        # Basic data validation and sorting
        df = df.sort("datetime").drop_nulls(subset=['close', 'high', 'low', 'open', 'volume', 'datetime'])
        if len(df) < 20:  # Reduced from 50 for quick test
            logger.warning(f"Insufficient data for {strategy_name}: {len(df)} rows")
            return None

        logger.debug(f"Data validated: {len(df)} rows")

        # Generate signals with detailed logging
        logger.debug(f"Generating long signals for {strategy_name}")
        long_signals = generate_strategy_signals(df, "long", strategy)
        logger.debug(f"Long signals generated: {long_signals.sum()}")
        
        logger.debug(f"Generating short signals for {strategy_name}")
        short_signals = generate_strategy_signals(df, "short", strategy)
        logger.debug(f"Short signals generated: {short_signals.sum()}")
        
        # Apply relaxed distance filtering
        # Only filter if there are signals to avoid unnecessary computation
        if long_signals.sum() > 0:
            long_signals = filter_signals_by_distance(long_signals, min_distance=1)  # Very relaxed
            logger.debug(f"Long signals after filtering: {long_signals.sum()}")
        
        if short_signals.sum() > 0:
            short_signals = filter_signals_by_distance(short_signals, min_distance=1)  # Very relaxed
            logger.debug(f"Short signals after filtering: {short_signals.sum()}")
        
        # Filter to only signal rows once (this is for logging/early exit, not for vectorbt input)
        # The actual signals passed to vectorbt are long_signals and short_signals directly.
        if long_signals.sum() == 0 and short_signals.sum() == 0:
            logger.warning(f"No signals generated or found after filtering for {strategy_name}")
            return None

        return process_signals_vectorized(df, long_signals, short_signals, strategy, rr, timeframe)
        
    except Exception as e:
        logger.error(f"Vectorized trade simulation failed for {strategy.get('name', 'Unknown')}: {e}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        return None

def calculate_performance_metrics(trades: List[Dict[str, Any]], symbol: str, strategy_name: str, timeframe: str, rr_combo: List[float]) -> Optional[Dict[str, Any]]:
    """Calculate performance metrics with VERY RELAXED validation for debugging"""
    if not trades:
        logger.debug(f"No trades provided for {symbol} {strategy_name} {timeframe}")
        return None
    
    logger.debug(f"Calculating metrics for {len(trades)} trades")
    
    total_pnl = sum(t['pnl'] for t in trades)
    total_pnl_pct = sum(t['pnl_pct'] for t in trades)
    winning_trades = sum(1 for t in trades if t['pnl'] > 0)
    total_trades = len(trades)
    
    accuracy = winning_trades / total_trades if total_trades > 0 else 0
    expectancy = total_pnl / total_trades if total_trades > 0 else 0
    
    avg_win = sum(t['pnl'] for t in trades if t['pnl'] > 0) / winning_trades if winning_trades > 0 else 0
    losing_trades = total_trades - winning_trades
    avg_loss = sum(t['pnl'] for t in trades if t['pnl'] < 0) / losing_trades if losing_trades > 0 else 0
    
    profit_factor = avg_win / abs(avg_loss) if avg_loss != 0 else float('inf')
    
    # Calculate drawdown
    cumulative_pnl = []
    running_pnl = 0
    for trade in trades:
        running_pnl += trade['pnl_pct']
        cumulative_pnl.append(running_pnl)
    
    max_drawdown = 0
    if cumulative_pnl:
        peak = cumulative_pnl[0]
        for pnl in cumulative_pnl:
            if pnl > peak:
                peak = pnl
            drawdown = peak - pnl
            if drawdown > max_drawdown:
                max_drawdown = drawdown
    
    # Calculate Sharpe ratio
    pnl_series_pl = pl.Series([t['pnl_pct'] for t in trades])
    if len(pnl_series_pl) > 1:
        std_dev = pnl_series_pl.std()
        sharpe_ratio = (pnl_series_pl.mean() * math.sqrt(252)) / std_dev if std_dev > 0 else 0
    else:
        sharpe_ratio = 0
    
    result = {
        'stock_name': symbol,
        'strategy_name': strategy_name,
        'timeframe': timeframe,
        'risk_reward_ratio': f"{rr_combo[0]}:{rr_combo[1]}",
        'total_trades': total_trades,
        'winning_trades': winning_trades,
        'accuracy': round(accuracy * 100, 2),
        'total_pnl': round(total_pnl, 2),
        'roi': round(total_pnl_pct, 2),
        'expectancy': round(expectancy, 2),
        'avg_win': round(avg_win, 2),
        'avg_loss': round(avg_loss, 2),
        'profit_factor': round(profit_factor, 2),
        'max_drawdown': round(max_drawdown, 2),
        'sharpe_ratio': round(sharpe_ratio, 2),
        'avg_holding_period': round(sum(t['holding_period'] for t in trades) / total_trades, 1),
        'is_profitable': total_pnl_pct > PROFIT_THRESHOLD
    }
    
    logger.debug(f"Metrics calculated: {result}")
    return result

# File loading and processing functions
def load_strategies() -> List[Dict[str, Any]]:
    """Load strategies from YAML file with proper Windows encoding"""
    try:
        # Fix Windows encoding issue - explicitly use UTF-8
        with open(STRATEGIES_FILE, 'r', encoding='utf-8') as f:
            data = yaml.safe_load(f)
        strategies = data.get('strategies', [])
        logger.info(f"[LOAD] Loaded {len(strategies)} strategies")
        return strategies
    except Exception as e:
        logger.error(f"Failed to load strategies from {STRATEGIES_FILE}: {e}")
        return []

def get_available_feature_files() -> List[Tuple[str, str, str]]:
    """Get available feature files"""
    feature_files = []
    for file_path in Path(DATA_DIR).glob("*.parquet"):
        filename = file_path.name
        symbol, timeframe = extract_symbol_and_timeframe_from_filename(filename)
        if symbol and timeframe and timeframe in TIMEFRAMES:
            feature_files.append((str(file_path), symbol, timeframe))
    logger.info(f"[FOUND] {len(feature_files)} feature files")
    return feature_files

def extract_symbol_and_timeframe_from_filename(filename: str) -> Tuple[Optional[str], Optional[str]]:
    """Extract symbol and timeframe from filename"""
    try:
        stem = Path(filename).stem
        if stem.startswith("features_"):
            stem = stem[9:]
        parts = stem.split('_')
        timeframe = parts[-1] if parts[-1] in TIMEFRAMES else None
        symbol = '_'.join(parts[:-1]) if timeframe else None
        return symbol, timeframe
    except Exception as e:
        logger.debug(f"Failed to extract symbol/timeframe from {filename}: {e}")
    return None, None

def generate_output_filename(symbol: str, timeframe: str) -> str:
    """Generate output filename"""
    return f"backtest_{symbol}_{timeframe}.parquet"

def generate_temp_filename(symbol: str, timeframe: str, strategy_name: str, rr_combo: List[float]) -> str:
    """Generate a unique temporary filename for each strategy, timeframe, stock, and RR combination."""
    rr_str = f"{rr_combo[0]}_{rr_combo[1]}".replace(".", "_")
    return f"temp_backtest_{symbol}_{timeframe}_{strategy_name}_{rr_str}.parquet"

async def write_symbol_results_async(results: List[Dict[str, Any]], symbol: str, timeframe: str):
    """Write results to file"""
    if not results:
        return
    
    try:
        output_filename = generate_output_filename(symbol, timeframe)
        output_path = os.path.join(OUTPUT_DIR, output_filename)
        
        # Ensure output directory exists
        Path(OUTPUT_DIR).mkdir(parents=True, exist_ok=True)
        
        # Write results
        pl.DataFrame(results).write_parquet(output_path, compression=COMPRESSION)
        logger.info(f"[SUCCESS] Written {len(results)} results for {symbol} to {output_filename}")
    except Exception as e:
        logger.error(f"[ERROR] Error writing results for {symbol}: {e}")

def process_single_file_sync(file_info: Tuple[str, str, str], strategies: List[Dict[str, Any]], risk_reward_ratios: List[List[float]]) -> List[Dict[str, Any]]:
    """Synchronous function to process a single file for backtesting."""
    file_path, symbol, timeframe = file_info
    file_results = []
    
    start_time = time.time()
    logger.info(f"Processing {symbol} ({timeframe})...")
    
    try:
        df = pl.read_parquet(file_path)
        logger.info(f"Loaded {len(df)} rows for {symbol}")
        
        required_cols = ['datetime', 'open', 'high', 'low', 'close', 'volume']
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            logger.error(f"Missing required columns for {symbol}: {missing_cols}")
            return []
        
        for strategy_idx, strategy in enumerate(strategies):
            strategy_name = strategy.get('name', f'Strategy_{strategy_idx}')
            logger.info(f"  Strategy {strategy_idx + 1}/{len(strategies)}: {strategy_name}")
            
            for rr_idx, rr in enumerate(risk_reward_ratios):
                rr_ratio_str = f"{rr[0]}:{rr[1]}"
                logger.debug(f"    Processing RR {rr_ratio_str}")
                trades = simulate_trades_vectorized(df, strategy, rr, timeframe) # Now synchronous
                
                logger.debug(f"    Generated {len(trades) if trades else 0} trades for RR {rr_ratio_str}")
                
                if trades and len(trades) >= 1:
                    metrics = calculate_performance_metrics(trades, symbol, strategy_name, timeframe, rr)
                    if metrics:
                        file_results.append(metrics)
                        logger.info(f"    Added metrics for RR {rr_ratio_str} - {len(trades)} trades")
                else:
                    logger.debug(f"    No valid trades for RR {rr_ratio_str}")
        
        file_time = time.time() - start_time
        logger.info(f"  File processed in {file_time:.2f}s")
        
    except Exception as e:
        logger.error(f"Error processing {symbol}: {e}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        
    return file_results

# Utility functions
def aggressive_memory_cleanup():
    """Aggressive memory cleanup"""
    gc.collect()

def reset_polars_state():
    """Reset Polars state"""
    pass

# Main async function
async def main_async():
    """Main asynchronous entry point"""
    logger.info("[INIT] Starting Enhanced Backtesting System - PERFORMANCE OPTIMIZED")
    logger.info("=" * 80)
    
    start_time = time.time()
    
    # Load strategies
    strategies = load_strategies()
    if not strategies:
        logger.error("[ERROR] No strategies loaded, exiting")
        return
    
    # Get files
    feature_files = get_available_feature_files()
    if not feature_files:
        logger.error("[ERROR] No feature files found, exiting")
        return
    
    logger.info(f"[INFO] Processing {len(feature_files)} files with {len(strategies)} strategies")
    
    # Ensure temporary directory exists
    Path(TEMP_DIR).mkdir(parents=True, exist_ok=True)

    init_executors() # Initialize the executor

    total_files = len(feature_files)

    if USE_PROCESS_POOL_EXECUTOR and process_executor:
        logger.info(f"[INFO] Using ProcessPoolExecutor for parallel file processing with {CONCURRENT_FILES} workers.")
        loop = asyncio.get_running_loop()
        tasks = []
        
        # Group tasks by (symbol, timeframe) for final consolidation
        file_timeframe_combinations = {} # Key: (symbol, timeframe), Value: List of futures for that combination

        for file_info in feature_files:
            file_path, symbol, timeframe = file_info
            if (symbol, timeframe) not in file_timeframe_combinations:
                file_timeframe_combinations[(symbol, timeframe)] = []

            for strategy in strategies:
                for rr in RISK_REWARD_RATIOS:
                    task = loop.run_in_executor(
                        process_executor,
                        process_file_strategy_rr,
                        file_info, strategy, rr
                    )
                    file_timeframe_combinations[(symbol, timeframe)].append(task)

        # Process results for each (symbol, timeframe) combination
        for (symbol, timeframe), futures_list in file_timeframe_combinations.items():
            combined_results_for_symbol_timeframe = []
            temp_files_to_clean = []

            for future in asyncio.as_completed(futures_list):
                try:
                    temp_file_path, result_metrics = await future
                    if result_metrics:
                        combined_results_for_symbol_timeframe.extend(result_metrics)
                    if temp_file_path:
                        temp_files_to_clean.append(temp_file_path)
                except Exception as e:
                    logger.error(f"Task for {symbol} ({timeframe}) failed: {e}")
            
            # Write consolidated results for the (symbol, timeframe)
            if combined_results_for_symbol_timeframe:
                await write_symbol_results_async(combined_results_for_symbol_timeframe, symbol, timeframe)
            
            # Clean up temporary files for this (symbol, timeframe)
            for temp_file in temp_files_to_clean:
                try:
                    os.remove(temp_file)
                    logger.debug(f"Cleaned up temporary file: {temp_file}")
                except OSError as e:
                    logger.warning(f"Error cleaning up temporary file {temp_file}: {e}")

    else:
        logger.info("[INFO] Running in sequential mode.")
        # Sequential processing (original logic, adapted to call process_single_file_sync)
        for idx, file_info in enumerate(feature_files, 1):
            results_from_file = process_single_file_sync(file_info, strategies, RISK_REWARD_RATIOS)
            await write_symbol_results_async(results_from_file, file_info[1], file_info[2]) # Write directly
            logger.info(f"Completed {idx}/{total_files} files. Collected {len(results_from_file)} results.")
            if idx % 10 == 0:
                gc.collect()
                logger.info("Sequential mode memory cleanup performed.")

    cleanup_executors() # Shut down the executor

    # Summary
    end_time = time.time()
    # In parallel mode, all_backtest_results is not directly accumulated here, but written per symbol/timeframe
    # So, we'll just log the total time and files processed.
    logger.info("[SUCCESS] BACKTESTING COMPLETED!")
    logger.info(f"[TIME] Total time: {total_time:.1f} seconds")
    logger.info(f"[FILES] Files processed: {total_files}")
    
    # Output summary
    output_files = list(Path(OUTPUT_DIR).glob("backtest_*.parquet"))
    if output_files:
        total_size = sum(f.stat().st_size for f in output_files) / (1024 * 1024)
        logger.info(f"[OUTPUT] Generated {len(output_files)} files ({total_size:.1f} MB)")

def process_file_strategy_rr(file_info, strategy, rr):
    file_path, symbol, timeframe = file_info
    strategy_name = strategy.get('name', 'UnknownStrategy')
    temp_file_path = None
    try:
        df = pl.read_parquet(file_path)
        if len(df) < 1000:
            return None, [] # Return None for temp_file_path if no processing

        trades = simulate_trades_vectorized(df, strategy, rr, timeframe)
        
        result_metrics = []
        if trades and len(trades) >= 1:
            metrics = calculate_performance_metrics(trades, symbol, strategy_name, timeframe, rr)
            if metrics:
                result_metrics.append(metrics)
        
        # Write temporary file for this specific combination
        temp_filename = generate_temp_filename(symbol, timeframe, strategy_name, rr)
        temp_file_path = os.path.join(TEMP_DIR, temp_filename)
        
        if result_metrics:
            pl.DataFrame(result_metrics).write_parquet(temp_file_path, compression=COMPRESSION)
            logger.debug(f"Written temporary results to {temp_file_path}")
        else:
            # Create an empty parquet file if no results, to mark completion
            pl.DataFrame().write_parquet(temp_file_path, compression=COMPRESSION)
            logger.debug(f"Created empty temporary file for {symbol} | {strategy_name} | {rr}")

        # Aggressive memory cleanup after each combination
        aggressive_memory_cleanup()
        reset_polars_state()
        
        return temp_file_path, result_metrics
    except Exception as e:
        logger.error(f"Failed for {symbol} | {strategy_name} | {rr}: {e}")
        return temp_file_path, [] # Return temp_file_path even on failure for cleanup


# Main function for direct execution
async def main():
    """Main function for direct execution"""
    await main_async()


if __name__ == "__main__":
    asyncio.run(main())
